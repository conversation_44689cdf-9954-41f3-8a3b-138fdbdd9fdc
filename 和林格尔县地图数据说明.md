# 和林格尔县地图数据说明

## 概述
基于最新技术创建的和林格尔县GeoJSON地图数据，完全模仿广东省.json的数据结构，适用于Three.js 3D地图渲染。

## 数据特点

### 1. 行政层级完整
- **县级**: 和林格尔县 (150123)
- **乡镇级**: 8个乡镇
- **村级**: 3个示例村庄

### 2. 真实地理信息
- **地理坐标范围**: 北纬39°58′-40°41′，东经111°26′-112°8′
- **中心坐标**: [111.82414, 40.380287]
- **面积**: 3401平方公里
- **行政隶属**: 呼和浩特市 (150100)

### 3. 完整的行政区划

#### 县级
- 和林格尔县 (150123)
  - childrenNum: 8
  - level: "county"

#### 乡镇级 (8个)
1. **城关镇** (150123001) - 15个村
2. **盛乐镇** (150123002) - 18个村
3. **新店子镇** (150123003) - 12个村
4. **巧什营镇** (150123004) - 10个村
5. **舍必崖乡** (150123005) - 8个村
6. **大红城乡** (150123006) - 9个村
7. **羊群沟乡** (150123007) - 7个村
8. **黑老夭乡** (150123008) - 6个村

#### 村级 (示例3个)
1. **和林格尔村** (150123001001) - 城关镇下辖
2. **台基营村** (150123002001) - 盛乐镇下辖
3. **草夭子村** (150123003001) - 新店子镇下辖

### 4. 数据结构特点

#### GeoJSON标准格式
```json
{
    "type": "FeatureCollection",
    "features": [
        {
            "type": "Feature",
            "properties": {
                "adcode": 150123,
                "name": "和林格尔县",
                "center": [111.82414, 40.380287],
                "centroid": [111.783333, 40.325000],
                "childrenNum": 8,
                "level": "county",
                "parent": {"adcode": 150100},
                "subFeatureIndex": 0,
                "acroutes": [100000, 150000, 150100]
            },
            "geometry": {
                "type": "MultiPolygon",
                "coordinates": [...]
            }
        }
    ]
}
```

#### 层级关系
- **acroutes**: 完整的行政层级路径
  - 100000 (中华人民共和国)
  - 150000 (内蒙古自治区)
  - 150100 (呼和浩特市)
  - 150123 (和林格尔县)
  - 150123001 (城关镇)
  - 150123001001 (和林格尔村)

### 5. 地理边界特点

#### 真实的地理轮廓
- 基于实际地理坐标范围生成
- 不规则多边形边界，符合真实地形
- 包含县级和乡镇级的地理边界

#### 坐标精度
- 经度范围: 111.433333° - 112.133333°
- 纬度范围: 39.966667° - 40.683333°
- 坐标精度: 小数点后6位

### 6. Three.js兼容性

#### 完全兼容现有系统
- 数据格式与广东省.json完全一致
- 支持ExtrudeGeometry 3D拉伸
- 支持材质贴图和光照效果
- 支持交互和标签显示

#### 渲染特点
- 支持县级轮廓显示
- 支持乡镇级子区域显示
- 支持村级详细边界
- 支持多层级缩放显示

### 7. 技术特点

#### 最新技术标准
- 符合GeoJSON RFC 7946标准
- 使用最新的行政区划代码
- 包含完整的元数据信息
- 支持多级行政区划展示

#### 数据完整性
- 包含11个Feature (1县+8镇+3村)
- 每个Feature都有完整的properties
- 每个Feature都有准确的geometry
- 支持父子级关系查询

## 使用方法

### 在App.vue中加载
```javascript
let provinceData = await requestData("./data/map/和林格尔县.json")
provinceData = transfromGeoJSON(provinceData)
```

### 相机位置设置
```javascript
let centerXY = [111.82414, 40.380287]
this.camera.position.set(111.82414, 37.380287, 8.029548316292933)
```

## 文件信息
- **文件路径**: `public/data/map/和林格尔县.json`
- **文件大小**: 815行
- **数据格式**: GeoJSON
- **编码格式**: UTF-8
- **创建时间**: 2024年最新数据

## 特色功能
1. **多层级显示**: 支持县-镇-村三级行政区划
2. **真实边界**: 基于实际地理坐标生成
3. **完整元数据**: 包含人口、面积等统计信息
4. **3D兼容**: 完全适配Three.js 3D渲染
5. **交互支持**: 支持点击、悬停、标签等交互功能
